package com.phonecheck.syslog;

import com.phonecheck.model.constants.FileConstants;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.syslog.SysLogFileInfo;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

@Service
public class SysLogFileWriterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SysLogFileWriterService.class);

    private final SupportFilePath supportFilePath;
    private final FileUtil fileUtil;

    public SysLogFileWriterService(final SupportFilePath supportFilePath, final FileUtil fileUtil) {
        this.supportFilePath = supportFilePath;
        this.fileUtil = fileUtil;
    }

    /**
     * Starts logging for the specified device by initializing a file writer service.
     * It opens a file for writing logs specific to the device and writes an initial log entry.
     *
     * @param device The Device object for which logging is started.
     * @return SysLogFileInfo object
     */
    public SysLogFileInfo createSysLogFile(final Device device) {
        try {
            final File sysLogFile = getDeviceLogFile(device);
            final BufferedWriter writer = new BufferedWriter(new FileWriter(sysLogFile, true));
            writer.write("Logging started for device: " + device.getSerial() + System.lineSeparator());
            writer.flush();
            return SysLogFileInfo.builder().
                    sysLogFile(sysLogFile).
                    sysLogFileWriter(writer).
                    build();
        } catch (IOException e) {
            LOGGER.error("Error while starting file writer service for device id {}", device.getId(), e);
            return null;
        }
    }

    /**
     * Writes a log message to the device-specific log file.
     * If the writer is null, the message will not be logged.
     *
     * @param writer  buffered writer for given device syslog file
     * @param message The log message to be written.
     */
    public void appendLogToSysLogFile(final BufferedWriter writer, final String message) {
        if (writer != null) {
            try {
                writer.write(message + System.lineSeparator());
                writer.flush();
            } catch (IOException e) {
                LOGGER.error("Error while writing log into file ", e);
            }
        }
    }

    /**
     * Stops logging for the current device by closing the file writer service.
     * It writes a final log entry indicating logging has stopped for the device and deletes the log file.
     *
     * @param sysLogFileInfo given device SysLogFileInfo
     */
    public void stopSysLogFileWriter(final SysLogFileInfo sysLogFileInfo) {
        if (sysLogFileInfo != null && sysLogFileInfo.getSysLogFileWriter() != null) {
            final BufferedWriter writer = sysLogFileInfo.getSysLogFileWriter();
            try {
                writer.write("Logging stopped" + System.lineSeparator());
                writer.flush();
                writer.close();
                // Delete file
                if (sysLogFileInfo.getSysLogFile() != null) {
                    if (fileUtil.deleteFile(sysLogFileInfo.getSysLogFile().getAbsolutePath())) {
                        LOGGER.error("Failed to delete log file {} ", sysLogFileInfo.getSysLogFile().getAbsolutePath());
                    }
                } else {
                    LOGGER.error("Syslog file on system does not exist");
                }
            } catch (IOException e) {
                LOGGER.error("Error while stopping file writer service for device", e);
            }
        } else {
            LOGGER.warn("File writer is already null for device");
        }
    }

    /**
     * Retrieves the device-specific log file for the given device.
     * If the file does not exist, it creates a new one.
     *
     * @param device The Device object for which the log file is retrieved.
     * @return A File object representing the device-specific log file.
     * @throws IOException If an I/O error occurs while creating the log file.
     */
    private File getDeviceLogFile(final Device device) throws IOException {
        String deviceLogsPath = supportFilePath.getPaths().getRootFolderPath() + File.separator +
                (device instanceof AndroidDevice ? FileConstants.ANDROID_LOGS_FOLDER
                        : FileConstants.IOS_LOGS_FOLDER) + File.separator + device.getSerial() + ".log";
        return fileUtil.createFile(deviceLogsPath);
    }
}
