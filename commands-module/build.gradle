import org.springframework.boot.gradle.plugin.SpringBootPlugin

plugins {
    id 'java'
    id 'org.springframework.boot' version '3.0.5' apply false
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id "io.freefair.lombok" version "6.5.0.3"
    id 'checkstyle'
    id 'jacoco'
}

group 'com.phonecheck'
version '1.0'

repositories {
    mavenCentral()
}

dependencies {
    implementation project(':model')
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'
    implementation group: 'commons-io', name: 'commons-io', version: '2.11.0'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'commons-codec:commons-codec:1.15'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4.1'
    implementation 'javax.xml.bind:jaxb-api:2.3.1'

    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.6.1'
}

test {
    useJUnitPlatform()
    finalizedBy(jacocoTestReport)
}

dependencyManagement {
    imports {
        mavenBom SpringBootPlugin.BOM_COORDINATES
    }
}


jacoco {
    toolVersion = "0.8.12"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation=layout.buildDirectory.dir("$buildDir/reports/jacoco")
    }
}

jacocoTestCoverageVerification {
    dependsOn jacocoTestReport // tests are required to run before generating the report
    violationRules {
        rule {
            limit {
                minimum = 0.0
            }
        }
    }
}

check.dependsOn(jacocoTestCoverageVerification)