package com.phonecheck.backend.service;

import com.phonecheck.api.cloud.CloudTransactionService;
import com.phonecheck.dao.model.TblLastVendorDetail;
import com.phonecheck.dao.service.VendorDetailDBService;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class TransactionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionService.class);
    private final InMemoryStore inMemoryStore;
    private final CloudTransactionService cloudTransactionService;
    private final VendorDetailDBService vendorDetailDBService;

    /**
     * Creates a new transaction
     *
     * @return a newly created transaction
     */
    public Transaction createNewTransaction() {
        LOGGER.info("Creating new transaction");
        Transaction newTransaction = cloudTransactionService.createTransactionFromCloud(
                Transaction.newInstance(inMemoryStore.getUserName(), inMemoryStore.getLicenseId()));
        if (newTransaction != null) {
            vendorDetailDBService.createTransaction(newTransaction);
            inMemoryStore.setTransaction(newTransaction);
            vendorDetailDBService.setCurrentTransaction(newTransaction);
        } else {
            LOGGER.error("Could not create the new transaction");
        }
        // TODO implement ClearHashCodes();

        return newTransaction;
    }

    /**
     * Loads the last transaction as the active one.
     *
     * @param licenseId
     * @return transaction object
     */
    public Transaction loadPreviousTransaction(final Integer licenseId) {
        TblLastVendorDetail lastVendorDetail = vendorDetailDBService.getLastVendorByLicenseId(licenseId);
        if (lastVendorDetail == null) {
            return createNewTransaction();
        } else {
            final String transactionId = lastVendorDetail.getTransactionId();
            Transaction existingTransaction = vendorDetailDBService.getTransactionById(Integer.valueOf(transactionId));
            inMemoryStore.setTransaction(existingTransaction);
            return existingTransaction;
        }
    }
}