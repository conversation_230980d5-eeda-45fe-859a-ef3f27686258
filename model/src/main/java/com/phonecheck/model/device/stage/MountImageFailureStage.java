package com.phonecheck.model.device.stage;

import lombok.Builder;
import lombok.Data;

/**
 * Models a device's successful disk image mounting
 */
@Data
@Builder
public class MountImageFailureStage implements IStage {
    private final DeviceStage stage = DeviceStage.DEV_IMAGE_MOUNT_FAILED;
    private String id;
    private String transactionId;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}