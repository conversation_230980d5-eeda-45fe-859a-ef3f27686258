package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.AndroidShopfloorService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.device.results.file.push.BatteryService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceMeidInfoEvent;
import com.phonecheck.model.event.device.android.*;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.util.CustomizationUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AndroidPostImeiAcquisitionSuccessListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private BatteryService batteryService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidPostImeiAcquisitionSuccessListener listener;
    @Mock
    private VendorCriteriaService vendorCriteriaService;

    @Mock
    private CustomizationUtil customizationUtil;

    @Mock
    private AndroidShopfloorService androidShopfloorService;

    @BeforeEach
    void setUp() {
        listener = new AndroidPostImeiAcquisitionSuccessListener(objectMapper,
                mqttClient, eventPublisher, deviceConnectionTracker, inMemoryStore,
                batteryService, vendorCriteriaService, customizationUtil, androidShopfloorService);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
    }

    @Test
    void testOnEvent() throws MqttException {
        AndroidDevice device = new AndroidDevice();
        device.setId("111");
        device.setImei("12345");
        device.setImei2("3333");

        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);
        final AndroidPostImeiAcquisitionSuccessEvent event = new AndroidPostImeiAcquisitionSuccessEvent(
                listener, device, true);
        CloudCustomizationResponse customizationResponse =
                CloudCustomizationResponse.builder().advancedSettings(null).build();

        listener.onEvent(event);

        try {
            Thread.sleep(2000);
        } catch (Exception e) {
            // do nothing
        }

        verify(mqttClient, never()).publish(any(), any());

        verify(deviceConnectionTracker).getDevice(anyString());
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidPerformCustomizationEvent.class));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(DeviceMeidInfoEvent.class));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidKnoxInfoEvent.class));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidMdmAcquisitionEvent.class));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidPcUtilityInstallEvent.class));
        Mockito.verify(eventPublisher).publishEvent(Mockito.any(AndroidAppInstallEvent.class));
    }
}
