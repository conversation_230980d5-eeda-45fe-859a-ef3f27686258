package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when panic full result is available at the UI end.
 */
@Getter
@Setter
public class IosPanicCheckResultUpdateEvent extends AbstractDeviceEvent {
    private String panicFullResult;

    public IosPanicCheckResultUpdateEvent(Object source, Device device) {
        super(source, device);
    }
}
