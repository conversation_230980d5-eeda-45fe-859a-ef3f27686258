package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.subscriber.device.ios.IosManualFaceIdStatusSubscriber;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.event.device.ios.IosManualFaceIdStatusEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.ios.IosManualFaceIdStatusMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosManualFaceIdStatusSubscriberTest {
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private final ObjectMapper mapper = new ObjectMapper();
    private IosManualFaceIdStatusSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosManualFaceIdStatusSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessageManualFaceIdStatus() throws JsonProcessingException {
        MqttTopicMessage message = new MqttTopicMessage(DeviceType.IPHONE +
                "manual-face-id/request", mapper.writeValueAsString(new IosManualFaceIdStatusMessage()));
        subscriber.onMessage(message);
        verify(eventPublisher).publishEvent(any(IosManualFaceIdStatusEvent.class));
    }
}
