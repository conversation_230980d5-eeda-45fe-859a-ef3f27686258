package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.IosModelResponse;
import com.phonecheck.model.service.IosModelDb;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudIosModelDbServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudIosModelService cloudIosModelService;

    @BeforeEach
    public void setup() {
        cloudIosModelService = new CloudIosModelService(cloudApiRestClient);
    }

    @Test
    @DisplayName("Get Ios Model")
    public void testGetIosModels() {
        String version = "1212";
        IosModelResponse testResponse = new IosModelResponse();
        testResponse.setIosModels(List.of(new IosModelResponse.IosModel()));

        when(cloudApiRestClient.getLatestIosModels(version)).thenReturn(testResponse);

        IosModelDb result = cloudIosModelService.getIosModels(version);
        Assertions.assertEquals(testResponse.getIosModels(), result.getIosModels());
    }
}
