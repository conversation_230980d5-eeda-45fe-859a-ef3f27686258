package com.phonecheck.backend.subscriber.vendor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.event.vendor.VendorUpdateRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.VendorUpdateRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to a vendor details update request made from the UI
 */
@Component
@AllArgsConstructor
public class VendorUpdateRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(VendorUpdateRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("vendor-update", "request")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("Vendor update request received: {}", payload);
        try {
            final VendorUpdateRequestMessage request = mapper.readValue(payload,
                    VendorUpdateRequestMessage.class);

            eventPublisher.publishEvent(new VendorUpdateRequestEvent(this, request.getVendorName(),
                    request.getInvoiceNo(), request.getQty(), request.getBoxNo()));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
