package com.phonecheck.backend.listener.device.ios.precheck;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.device.activation.IosActivateDeviceService;
import com.phonecheck.info.ios.PreCheckStreamService;
import com.phonecheck.info.ios.PreCheckTestResultsService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.device.stage.AppTestingDoneStage;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.ios.IosActivationFailureEvent;
import com.phonecheck.model.event.device.ios.precheck.PreCheckInfoResultsRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceTestedMessage;
import com.phonecheck.model.mqtt.messages.ios.IosReadyStatusMessage;
import com.phonecheck.model.mqtt.messages.ios.PreCheckInfoTestRequestMessage;
import com.phonecheck.model.status.ActivationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.PreCheckUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class PreCheckInfoResultsRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(PreCheckInfoResultsRequestListener.class);

    private final PreCheckStreamService preCheckStreamService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final DeviceAutomationQueueService automationQueueService;
    private final PreCheckTestResultsService preCheckTestResultsService;
    private final InMemoryStore inMemoryStore;
    private final IosActivateDeviceService iosActivateDeviceService;
    private final ApplicationEventPublisher eventPublisher;
    private final DeviceStageUpdater stageUpdater;

    public PreCheckInfoResultsRequestListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                               final DeviceConnectionTracker deviceConnectionTracker,
                                               final DeviceAutomationQueueService automationQueueService,
                                               final PreCheckStreamService preCheckStreamService,
                                               final PreCheckTestResultsService preCheckTestResultsService,
                                               final InMemoryStore inMemoryStore,
                                               final IosActivateDeviceService iosActivateDeviceService,
                                               final DeviceStageUpdater stageUpdater,
                                               final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.automationQueueService = automationQueueService;
        this.preCheckStreamService = preCheckStreamService;
        this.preCheckTestResultsService = preCheckTestResultsService;
        this.inMemoryStore = inMemoryStore;
        this.iosActivateDeviceService = iosActivateDeviceService;
        this.eventPublisher = eventPublisher;
        this.stageUpdater = stageUpdater;
    }

    @Async
    @EventListener
    public void onEvent(final PreCheckInfoResultsRequestEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Pre check to be performed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("PreCheck device info tests initiated");
        // Stop pre-check stream routine
        preCheckStreamService.stopPreCheckStreamRoutine(deviceInTracker);

        PreCheckInfo trackerPreCheckInfo = deviceInTracker.getPreCheckInfo();
        PreCheckInfo.DeviceInfoResults infoResults;
        if (trackerPreCheckInfo.getDeviceInfoResults() == null) {
            LOGGER.info("Info result not found in tracker device");
            infoResults = PreCheckInfo.DeviceInfoResults.builder()
                    .lcdColor("Pending").digitizerTest("Pending").speaker("Pending").build();
        } else {
            LOGGER.info("Found info test results in tracker device: {}", trackerPreCheckInfo.getDeviceInfoResults());
            infoResults = trackerPreCheckInfo.getDeviceInfoResults();
        }

        trackerPreCheckInfo.setDeviceInfoResults(infoResults);
        // Update test results into tracker device according to pre-check-info
        preCheckTestResultsService.mapInfoResultsToDeviceTestResults(deviceInTracker);
        String processTag = PreCheckUtil.getProcessNameTag(deviceInTracker);

        if (StringUtils.isNotBlank(processTag)) {
            LOGGER.info("PreCheck info test pending, process tag: {}", processTag);
            // Notify UI
            final String topic = TopicBuilder.build(deviceInTracker, "pre-check", "info-results", "request");
            final PreCheckInfoTestRequestMessage infoTestRequestMessage = new PreCheckInfoTestRequestMessage();
            infoTestRequestMessage.setId(deviceInTracker.getId());
            infoTestRequestMessage.setInfoResults(infoResults);
            publishToMqttTopic(topic, infoTestRequestMessage);

            // Start Info test on connected device
            preCheckStartDeviceInfoProcess(deviceInTracker);
        } else {
            LOGGER.info("All PreCheck info tests performed successfully");
            // All test performed set ready status
            deviceInTracker.setStage(DeviceStage.READY);
            deviceInTracker.getDeviceTestResult().getTestResults().setTestingCompleted(true);

            // Update stage : Testing done
            AppTestingDoneStage stage = AppTestingDoneStage.builder()
                    .id(deviceInTracker.getId())
                    .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                    .licenseId(String.valueOf(inMemoryStore.getLicenseId()))
                    .serial(deviceInTracker.getSerial())
                    .deviceType(deviceInTracker.getDeviceType())
                    .deviceTestResult(deviceInTracker.getDeviceTestResult())
                    .timestamp(System.currentTimeMillis())
                    .build();
            stageUpdater.update(stage);

            // Tested : Notify UI
            final String testedTopic = TopicBuilder.build(deviceInTracker, "tested");
            final DeviceTestedMessage message = new DeviceTestedMessage();
            message.setId(deviceInTracker.getId());
            message.setDeviceTestResult(deviceInTracker.getDeviceTestResult());
            message.setVendorName(deviceInTracker.getVendorName());
            publishToMqttTopic(testedTopic, message);

            // Ready : Notify UI
            final String topic = TopicBuilder.build(deviceInTracker, "ready");
            final IosReadyStatusMessage readyMessage = new IosReadyStatusMessage();
            readyMessage.setId(deviceInTracker.getId());
            readyMessage.setReady(true);
            publishToMqttTopic(topic, readyMessage);

            activateDeviceIfRequired(deviceInTracker);
            // start test result automation
            automationQueueService
                    .enqueueDeviceAutomationRequest(new DeviceTestResultAutomationEvent(this, deviceInTracker));
        }

    }

    /**
     * Activates the given iOS device if the "ERASE" step is found in the test result workflow.
     * This method checks if the test result workflow contains an "ERASE" step. If found,
     * the device activation process is triggered. Otherwise, activation is skipped.
     * If an error occurs during activation, an {@link IosActivationFailureEvent} is published.
     *
     * @param device IosDevice to activate if required
     */
    private void activateDeviceIfRequired(final IosDevice device) {
        try {
            if (inMemoryStore.getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow()
                    .stream()
                    .anyMatch(automationStep ->
                            CloudCustomizationResponse.AutomationSteps.ERASE == automationStep)) {
                LOGGER.info("Erase step found in testresult automation activation will be required");
                iosActivateDeviceService.activate(device);
            } else {
                LOGGER.info("Erase step not found in test result automation activation not required");
            }
        } catch (IOException e) {
            LOGGER.error("Could not activate device ", e);
            eventPublisher.
                    publishEvent(new IosActivationFailureEvent(this, device, ActivationStatus.FAILED_UNKNOWN));
        }
    }

    /**
     * Starts the pre-check device info process for the given iOS device.
     * If the pre-check process tag is {@code TAG_LCD_TEST}, the method performs an LCD fluctuation test
     * by repeatedly starting and stopping the test process at 1-second intervals. The loop continues
     * until the running process tag changes, indicating that the UI has received a test result.
     * For other test tags, it simply starts the test process.
     *
     * @param iosDevice the iOS device for which the pre-check process is started
     */
    private void preCheckStartDeviceInfoProcess(final IosDevice iosDevice) {
        final String tag = iosDevice.getPreCheckInfo().getPreCheckRunningProcessTag();
        if (PreCheckUtil.TAG_LCD_TEST.equals(tag)) { // Fluctuation LCD test
            LOGGER.info("PreCheck LCD test started with fluctuation");
            int attempts = 0;
            do {
                try {

                    preCheckTestResultsService.startInfoTestProcess(iosDevice.getId(), PreCheckUtil.TAG_LCD_TEST);
                    Thread.sleep(1000);
                    preCheckTestResultsService.stopInfoTestProcess(iosDevice.getId(), PreCheckUtil.TAG_LCD_TEST);
                    Thread.sleep(1000);
                    if (!iosDevice.getPreCheckInfo().getPreCheckRunningProcessTag().equals(PreCheckUtil.TAG_LCD_TEST)) {
                        LOGGER.info("PreCheck LCD test result received from UI, stopping screen fluctuation");
                        break;
                    }
                    if (deviceConnectionTracker.getDevice(iosDevice.getId()) != null) {
                        LOGGER.info("Device removed from tracker during LCD test, stopping screen fluctuation");
                        break;
                    }
                } catch (InterruptedException e) {
                    // do nothing
                }
                attempts++;
            } while (attempts < 30);
        } else {
            LOGGER.info("PreCheck test started with tag {}", tag);
            preCheckTestResultsService.startInfoTestProcess(iosDevice.getId(), tag);
        }
    }
}